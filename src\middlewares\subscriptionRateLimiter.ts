import rateLimit from 'express-rate-limit';
import { Request, Response, NextFunction } from 'express';
import { AppError } from './errorHandler';
import { ApiKeyModel, SUBSCRIPTION_RATE_LIMITS } from '../models/ApiKey';
import logger from '../utils/logger';

// Default rate limit configuration
const DEFAULT_WINDOW_MS = 60000; // 1 minute
const DEFAULT_MAX_REQUESTS = 100; // Default to free tier

/**
 * Create a dynamic store based on memory (Redis not used for rate limiting)
 */
const createDynamicStore = (prefix: string) => {
  // Use memory store only - Redis API doesn't support rate limiting yet
  return undefined;
};

/**
 * Subscription-aware rate limiter that respects API key subscription tiers
 * while preserving RapidAPI authentication flow
 */
export const subscriptionRateLimiter = rateLimit({
  windowMs: DEFAULT_WINDOW_MS,
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many requests, please try again later.',
  handler: (_req: Request, _res: Response, next: NextFunction, options: any) => {
    next(new AppError(options.message, 429));
  },
  store: createDynamicStore('subscription'),
  
  // Dynamic rate limit based on authentication type
  max: async (req: Request) => {
    try {
      // PRIORITY 1: RapidAPI users - use RapidAPI rate limits
      if ((req as any).rapidApi?.authenticated) {
        const planType = (req as any).rapidApi?.usage?.planType;
        
        // RapidAPI handles its own rate limiting, so we use a high limit
        // to avoid interfering with RapidAPI's rate limiting system
        if (planType === 'FREE') {
          return 200; // Conservative limit for free RapidAPI users
        } else if (planType === 'BASIC') {
          return 1000; // Higher limit for basic RapidAPI users
        } else if (planType === 'PRO' || planType === 'ULTRA') {
          return 5000; // Very high limit for premium RapidAPI users
        }
        
        // Default for unknown RapidAPI plans
        return 500;
      }
      
      // PRIORITY 2: Regular API key users - use subscription-based limits
      if (req.apiKey) {
        const apiKeyData = await ApiKeyModel.getByKeyWithSubscription(req.apiKey.key);
        
        if (apiKeyData && apiKeyData.rate_limit_tier) {
          logger.debug(`Using subscription rate limit: ${apiKeyData.rate_limit_tier} for subscription: ${apiKeyData.subscription_type}`);
          return apiKeyData.rate_limit_tier;
        }
        
        if (apiKeyData && apiKeyData.subscription_type) {
          const limit = SUBSCRIPTION_RATE_LIMITS[apiKeyData.subscription_type as keyof typeof SUBSCRIPTION_RATE_LIMITS];
          if (limit) {
            logger.debug(`Using default subscription rate limit: ${limit} for subscription: ${apiKeyData.subscription_type}`);
            return limit;
          }
        }
      }
      
      // PRIORITY 3: Unauthenticated or unknown - use default free tier limit
      return DEFAULT_MAX_REQUESTS;
      
    } catch (error) {
      logger.error('Error determining rate limit:', error instanceof Error ? error : new Error(String(error)));
      // Fallback to default limit on error
      return DEFAULT_MAX_REQUESTS;
    }
  },
  
  // Key generator for rate limiting
  keyGenerator: (req: Request) => {
    // Use RapidAPI user as the rate limit key if available
    if ((req as any).rapidApi?.user) {
      return `rapidapi:${(req as any).rapidApi.user}`;
    }
    
    // Use API key as the rate limit key if available
    if (req.apiKey?.key) {
      return `api-key:${req.apiKey.key}`;
    }
    
    // Fall back to IP address for unauthenticated requests
    return `ip:${req.ip}`;
  },
  
  // Skip rate limiting for internal API requests
  skip: (req: Request) => {
    // Skip for internal API requests
    const apiKey = req.headers['x-api-key'] as string;
    if (apiKey === process.env.INTERNAL_API_KEY) {
      return true;
    }
    
    // Skip for admin API requests
    const adminApiKey = req.headers['x-admin-api-key'] as string;
    if (adminApiKey === process.env.ADMIN_API_KEY) {
      return true;
    }
    
    return false;
  }
});

/**
 * Enhanced rate limiter that works with the existing authentication flow
 * This middleware should be applied after authentication middlewares
 */
export const enhancedRateLimiter = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Apply the subscription rate limiter
    subscriptionRateLimiter(req, res, next);
  } catch (error) {
    logger.error('Error in enhanced rate limiter:', error instanceof Error ? error : new Error(String(error)));
    next(error);
  }
};

// Export individual rate limiters for specific subscription tiers
export const freeRateLimiter = rateLimit({
  windowMs: DEFAULT_WINDOW_MS,
  max: SUBSCRIPTION_RATE_LIMITS.free,
  standardHeaders: true,
  legacyHeaders: false,
  store: createDynamicStore('free'),
  keyGenerator: (req: Request) => `free:${req.ip}`,
});

export const baseRateLimiter = rateLimit({
  windowMs: DEFAULT_WINDOW_MS,
  max: SUBSCRIPTION_RATE_LIMITS.base,
  standardHeaders: true,
  legacyHeaders: false,
  store: createDynamicStore('base'),
  keyGenerator: (req: Request) => `base:${req.apiKey?.key || req.ip}`,
});

export const premiumRateLimiter = rateLimit({
  windowMs: DEFAULT_WINDOW_MS,
  max: SUBSCRIPTION_RATE_LIMITS.premium,
  standardHeaders: true,
  legacyHeaders: false,
  store: createDynamicStore('premium'),
  keyGenerator: (req: Request) => `premium:${req.apiKey?.key || req.ip}`,
});
