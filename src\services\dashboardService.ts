import { executeReadQuery, executeWriteQuery } from '../utils/databaseRouter';
import { connectionPoolGuard } from '../utils/connectionPoolGuard';
import { getWritePoolHealth, getReadPoolHealth, type ConnectionHealth } from '../config/database-pools';
import logger from '../utils/logger';

// Cache for dashboard metrics to improve performance
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

class DashboardCache {
  private cache = new Map<string, CacheEntry<any>>();
  private readonly defaultTTL = 30000; // 30 seconds default TTL

  set<T>(key: string, data: T, ttl: number = this.defaultTTL): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return entry.data as T;
  }

  clear(): void {
    this.cache.clear();
  }

  // Cleanup expired entries
  cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
      }
    }
  }
}

const dashboardCache = new DashboardCache();

// Cleanup cache every 5 minutes
setInterval(() => {
  dashboardCache.cleanup();
}, 300000);

export interface InboxStats {
  total: number;
  active: number;
  createdToday: number;
  expired: number;
  expiringSoon: number;
  regional: Array<{ region: string; count: number }>;
}

export interface UserStats {
  apiKeys: {
    total: number;
    createdToday: number;
    activeToday: number;
    neverUsed: number;
  };
  subscriptions: Array<{ tier: string; count: number }>;
  rapidApiUsers: {
    estimatedActive: number;
    note: string;
  };
}

export interface RequestStats {
  totals: {
    requests: number;
    lastHour: number;
    last24h: number;
    errors: number;
    errorRate: string;
  };
  regional: Array<{
    region: string;
    requests: number;
    avgResponseTime: string;
    errors: number;
    errorRate: string;
  }>;
  trends: Array<{
    hour: string;
    requests: number;
    avgResponseTime: string;
  }>;
  topEndpoints: Array<{
    endpoint: string;
    requests: number;
    avgResponseTime: string;
  }>;
}

export interface SystemHealth {
  connectionPools: {
    write: {
      total: number;
      idle: number;
      waiting: number;
      utilization: string;
    };
    read: {
      total: number;
      idle: number;
      waiting: number;
      utilization: string;
    };
    guard: any;
  };
  database: {
    write: boolean;
    read: boolean;
    latency: { write: number; read: number };
  };
  memory: {
    rss: number;
    heapTotal: number;
    heapUsed: number;
    external: number;
    heapUtilization: string;
  };
  system: {
    uptime: number;
    region: string;
    instanceId: string;
    nodeVersion: string;
    platform: string;
    arch: string;
  };
}

export interface SecurityStats {
  events: {
    blockedRequests: number;
    rateLimited: number;
    slowValidations: number;
    blockedIPs: number;
  };
  attackPatterns: Array<{ type: string; count: number }>;
  connectionGuard: any;
}

export class DashboardService {
  
  async getInboxStats(): Promise<InboxStats> {
    const cacheKey = 'inbox_stats';
    const cached = dashboardCache.get<InboxStats>(cacheKey);
    if (cached) return cached;

    try {
      // Use Promise.all for parallel queries to improve performance
      const [totalResult, activeResult, creationRateResult, regionalResult, expiryResult] = await Promise.all([
        executeReadQuery('SELECT COUNT(*) as total FROM inboxes WHERE deleted_at IS NULL'),
        executeReadQuery(`
          SELECT COUNT(*) as active 
          FROM inboxes 
          WHERE deleted_at IS NULL 
          AND last_accessed_at > NOW() - INTERVAL '24 hours'
        `),
        executeReadQuery(`
          SELECT COUNT(*) as created_today
          FROM inboxes 
          WHERE created_at > NOW() - INTERVAL '24 hours'
        `),
        executeReadQuery(`
          SELECT 
            COALESCE(region, 'unknown') as region,
            COUNT(*) as count
          FROM inboxes 
          WHERE deleted_at IS NULL
          GROUP BY region
          ORDER BY count DESC
        `),
        executeReadQuery(`
          SELECT 
            COUNT(CASE WHEN expires_at < NOW() THEN 1 END) as expired,
            COUNT(CASE WHEN expires_at > NOW() AND expires_at < NOW() + INTERVAL '24 hours' THEN 1 END) as expiring_soon
          FROM inboxes 
          WHERE deleted_at IS NULL
        `)
      ]);

      const stats: InboxStats = {
        total: parseInt(totalResult.rows[0].total),
        active: parseInt(activeResult.rows[0].active),
        createdToday: parseInt(creationRateResult.rows[0].created_today),
        expired: parseInt(expiryResult.rows[0].expired),
        expiringSoon: parseInt(expiryResult.rows[0].expiring_soon),
        regional: regionalResult.rows.map(row => ({
          region: row.region,
          count: parseInt(row.count)
        }))
      };

      dashboardCache.set(cacheKey, stats, 60000); // Cache for 1 minute
      return stats;

    } catch (error) {
      logger.error('Dashboard service inbox stats error:', error as Error);
      throw error;
    }
  }

  async getUserStats(): Promise<UserStats> {
    const cacheKey = 'user_stats';
    const cached = dashboardCache.get<UserStats>(cacheKey);
    if (cached) return cached;

    try {
      const [apiKeyResult, subscriptionResult, rapidApiResult] = await Promise.all([
        executeReadQuery(`
          SELECT 
            COUNT(*) as total_keys,
            COUNT(CASE WHEN created_at > NOW() - INTERVAL '24 hours' THEN 1 END) as created_today,
            COUNT(CASE WHEN last_used_at > NOW() - INTERVAL '24 hours' THEN 1 END) as active_today,
            COUNT(CASE WHEN last_used_at IS NULL THEN 1 END) as never_used
          FROM api_keys 
          WHERE deleted_at IS NULL
        `),
        executeReadQuery(`
          SELECT 
            COALESCE(subscription_tier, 'free') as tier,
            COUNT(*) as count
          FROM api_keys 
          WHERE deleted_at IS NULL
          GROUP BY subscription_tier
          ORDER BY count DESC
        `),
        executeReadQuery(`
          SELECT COUNT(DISTINCT client_ip) as estimated_rapidapi_users
          FROM request_logs 
          WHERE created_at > NOW() - INTERVAL '24 hours'
          AND (
            headers->>'x-rapidapi-key' IS NOT NULL 
            OR headers->>'x-rapidapi-host' IS NOT NULL
          )
        `).catch(() => ({ rows: [{ estimated_rapidapi_users: '0' }] })) // Fallback if table doesn't exist
      ]);

      const stats: UserStats = {
        apiKeys: {
          total: parseInt(apiKeyResult.rows[0].total_keys),
          createdToday: parseInt(apiKeyResult.rows[0].created_today),
          activeToday: parseInt(apiKeyResult.rows[0].active_today),
          neverUsed: parseInt(apiKeyResult.rows[0].never_used)
        },
        subscriptions: subscriptionResult.rows.map(row => ({
          tier: row.tier,
          count: parseInt(row.count)
        })),
        rapidApiUsers: {
          estimatedActive: parseInt(rapidApiResult.rows[0]?.estimated_rapidapi_users || '0'),
          note: 'Estimated based on request headers in last 24 hours'
        }
      };

      dashboardCache.set(cacheKey, stats, 120000); // Cache for 2 minutes
      return stats;

    } catch (error) {
      logger.error('Dashboard service user stats error:', error as Error);
      throw error;
    }
  }

  async getSystemHealth(): Promise<SystemHealth> {
    const cacheKey = 'system_health';
    const cached = dashboardCache.get<SystemHealth>(cacheKey);
    if (cached) return cached;

    try {
      // Get connection pool statistics
      const writePoolHealth = getWritePoolHealth();
      const readPoolHealth = getReadPoolHealth();
      const connectionGuardStats = connectionPoolGuard.getUtilizationStats();

      // Get memory usage
      const memoryUsage = process.memoryUsage();

      // Get database connection test
      let dbHealth = { write: false, read: false, latency: { write: 0, read: 0 } };
      try {
        const [writeTest, readTest] = await Promise.all([
          (async () => {
            const start = Date.now();
            await executeWriteQuery('SELECT 1');
            return Date.now() - start;
          })(),
          (async () => {
            const start = Date.now();
            await executeReadQuery('SELECT 1');
            return Date.now() - start;
          })()
        ]);

        dbHealth = {
          write: true,
          read: true,
          latency: { write: writeTest, read: readTest }
        };
      } catch (error) {
        logger.error('Database health check failed:', error as Error);
      }

      const health: SystemHealth = {
        connectionPools: {
          write: {
            total: writePoolHealth.totalCount,
            idle: writePoolHealth.idleCount,
            waiting: writePoolHealth.waitingCount,
            utilization: writePoolHealth.totalCount > 0 
              ? ((writePoolHealth.totalCount - writePoolHealth.idleCount) / writePoolHealth.totalCount * 100).toFixed(2)
              : '0.00'
          },
          read: {
            total: readPoolHealth.totalCount,
            idle: readPoolHealth.idleCount,
            waiting: readPoolHealth.waitingCount,
            utilization: readPoolHealth.totalCount > 0 
              ? ((readPoolHealth.totalCount - readPoolHealth.idleCount) / readPoolHealth.totalCount * 100).toFixed(2)
              : '0.00'
          },
          guard: connectionGuardStats
        },
        database: dbHealth,
        memory: {
          rss: Math.round(memoryUsage.rss / 1024 / 1024),
          heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
          heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
          external: Math.round(memoryUsage.external / 1024 / 1024),
          heapUtilization: (memoryUsage.heapUsed / memoryUsage.heapTotal * 100).toFixed(2)
        },
        system: {
          uptime: Math.round(process.uptime()),
          region: process.env.REGION || 'unknown',
          instanceId: process.env.INSTANCE_ID || 'unknown',
          nodeVersion: process.version,
          platform: process.platform,
          arch: process.arch
        }
      };

      dashboardCache.set(cacheKey, health, 15000); // Cache for 15 seconds (more frequent updates for health)
      return health;

    } catch (error) {
      logger.error('Dashboard service system health error:', error as Error);
      throw error;
    }
  }

  async cleanupExpiredInboxes(): Promise<{ inboxes: number; emails: number; beforeCount: number }> {
    try {
      // Get count before cleanup
      const beforeResult = await executeReadQuery(`
        SELECT COUNT(*) as expired_count
        FROM inboxes 
        WHERE deleted_at IS NULL 
        AND expires_at < NOW()
      `);

      // Perform cleanup operations in parallel where possible
      const [cleanupResult, emailCleanupResult] = await Promise.all([
        executeWriteQuery(`
          UPDATE inboxes 
          SET deleted_at = NOW(), 
              updated_at = NOW()
          WHERE deleted_at IS NULL 
          AND expires_at < NOW()
        `),
        executeWriteQuery(`
          UPDATE emails 
          SET deleted_at = NOW(),
              updated_at = NOW()
          WHERE deleted_at IS NULL 
          AND inbox_id IN (
            SELECT id FROM inboxes 
            WHERE deleted_at IS NOT NULL 
            AND expires_at < NOW()
          )
        `)
      ]);

      // Clear relevant caches after cleanup
      dashboardCache.clear();

      return {
        inboxes: cleanupResult.rowCount || 0,
        emails: emailCleanupResult.rowCount || 0,
        beforeCount: parseInt(beforeResult.rows[0].expired_count)
      };

    } catch (error) {
      logger.error('Dashboard service cleanup error:', error as Error);
      throw error;
    }
  }

  // Clear all caches (useful for testing or manual refresh)
  clearCache(): void {
    dashboardCache.clear();
  }
}

export const dashboardService = new DashboardService();
