import { Request, Response, NextFunction } from 'express';
import logger from '../utils/logger';

// Regional security configuration
interface RegionalSecurityConfig {
  maxRequestSize: number;
  maxConcurrentRequests: number;
  ipCacheSize: number;
  validationTimeout: number;
  enableGeoBlocking: boolean;
  enableThreatIntel: boolean;
}

const REGIONAL_SECURITY_CONFIG: Record<string, RegionalSecurityConfig> = {
  IN: { // India - Ultra lightweight for 3-core server
    maxRequestSize: 1024 * 1024,      // 1MB
    maxConcurrentRequests: 50,         // Conservative limit
    ipCacheSize: 1000,                 // Small cache
    validationTimeout: 1,              // 1ms max
    enableGeoBlocking: false,          // Disable for performance
    enableThreatIntel: false,          // Disable for performance
  },
  US: { // US Primary - Full featured
    maxRequestSize: 5 * 1024 * 1024,   // 5MB
    maxConcurrentRequests: 200,
    ipCacheSize: 5000,
    validationTimeout: 5,              // 5ms max
    enableGeoBlocking: true,
    enableThreatIntel: true,
  },
  EU: { // EU - Balanced
    maxRequestSize: 3 * 1024 * 1024,   // 3MB
    maxConcurrentRequests: 150,
    ipCacheSize: 3000,
    validationTimeout: 3,              // 3ms max
    enableGeoBlocking: true,
    enableThreatIntel: false,
  }
};

// Security validation result
interface SecurityResult {
  blocked: boolean;
  reason?: string;
  severity?: number;
}

// IP security state for caching
interface IPSecurityState {
  blocked: boolean;
  blockedUntil: number;
  requestCount: number;
  lastRequest: number;
  threatLevel: number;
}

// Input normalization function
function normalizeInput(input: string): string {
  try {
    // URL decode
    input = decodeURIComponent(input);
  } catch (e) {
    // If decoding fails, continue with original input
  }

  // HTML entity removal (basic)
  input = input.replace(/&[#A-Za-z0-9]+;/g, '');

  // Remove null bytes and normalize whitespace
  input = input.replace(/\u0000/g, '')
               .replace(/\s+/g, ' ')
               .toLowerCase()
               .trim();

  return input;
}

// Enhanced attack patterns with scoring system
const ATTACK_PATTERNS = {
  // High-confidence dangerous characters/sequences (Score: 8)
  dangerousChars: {
    pattern: /[;|&`$<>]|(\$\()|(\$\{)|\r\n|\n|\x00/,
    score: 8,
    description: 'Command injection characters'
  },

  // SQL injection heuristics (Score: 7)
  sqlInjection: {
    pattern: /(\bunion\b.*\bselect\b)|(\bselect\b.*\bfrom\b)|(['"]\s*or\s+['"]?\d)|(--|#|\/\*)/,
    score: 7,
    description: 'SQL injection patterns'
  },

  // XSS patterns (Score: 6)
  xss: {
    pattern: /<\s*script\b|javascript:|on\w+\s*=|<\s*iframe\b|<\s*svg\b|<\s*img\b[^>]*on\w+\s*=/,
    score: 6,
    description: 'Cross-site scripting patterns'
  },

  // Path traversal (Score: 5)
  pathTraversal: {
    pattern: /(\.\.\/|\.\.\\|%2e%2e%2f|%2e%2e%5c)/i,
    score: 5,
    description: 'Path traversal patterns'
  },

  // SSRF patterns (Score: 7)
  ssrf: {
    pattern: /\bhttps?:\/\/(?:127\.0\.0\.1|localhost|169\.254\.169\.254|10\.\d{1,3}\.|192\.168\.|172\.(1[6-9]|2[0-9]|3[0-1])\.)/i,
    score: 7,
    description: 'Server-side request forgery patterns'
  },

  // CRLF/Header injection (Score: 6)
  crlf: {
    pattern: /\r\n.*(set-cookie|content-type|host|location):/i,
    score: 6,
    description: 'CRLF injection patterns'
  },

  // File inclusion/scheme abuse (Score: 5)
  fileInclusion: {
    pattern: /(?:file|php|zip|data|expect|dict|gopher):\/\//i,
    score: 5,
    description: 'File inclusion patterns'
  },

  // XXE patterns (Score: 6)
  xxe: {
    pattern: /<!ENTITY|<!DOCTYPE|SYSTEM\s+["']|<!ENTITY\s+%/i,
    score: 6,
    description: 'XML external entity patterns'
  },

  // Deserialization markers (Score: 5)
  deserialization: {
    pattern: /java\.lang\.|O:\d+:|R:\d+:|pickle|__reduce__|eval\(/i,
    score: 5,
    description: 'Deserialization attack patterns'
  },

  // Encoded payloads (Score: 4)
  encodedPayload: {
    pattern: /(%[0-9a-f]{2}){10,}|(\\u[0-9a-f]{4}){5,}|[A-Za-z0-9+\/]{50,}={0,2}/i,
    score: 4,
    description: 'Suspicious encoded content'
  },

  // Command execution context (Score: 6)
  commandContext: {
    pattern: /\b(curl|wget|nc|netcat|bash|sh|cmd|powershell|eval|exec|system)\b.*[;&|]/i,
    score: 6,
    description: 'Command execution in context'
  },

  // Template injection (Score: 5)
  templateInjection: {
    pattern: /\{\{.*\}\}|\$\{.*\}|<%.*%>|#\{.*\}/,
    score: 5,
    description: 'Template injection patterns'
  }
};

// Sliding window for rate limiting
class SlidingWindow {
  private requests: number[] = [];
  private readonly windowSize: number;

  constructor(windowSize: number) {
    this.windowSize = windowSize;
  }

  addRequest(timestamp: number): number {
    // Remove old requests outside the window
    const cutoff = timestamp - this.windowSize;
    this.requests = this.requests.filter(time => time > cutoff);
    
    // Add new request
    this.requests.push(timestamp);
    return this.requests.length;
  }

  isExpired(timestamp: number): boolean {
    const cutoff = timestamp - this.windowSize;
    return this.requests.length === 0 || this.requests[this.requests.length - 1] < cutoff;
  }
}

// Memory-efficient rate limiter
class RegionalRateLimiter {
  private windows = new Map<string, SlidingWindow>();
  private readonly windowSize: number = 60000; // 1 minute
  private readonly maxRequests: number;
  private lastCleanup: number = 0;
  private readonly cleanupInterval: number = 300000; // 5 minutes

  constructor(region: string) {
    const config = REGIONAL_SECURITY_CONFIG[region] || REGIONAL_SECURITY_CONFIG.US;
    this.maxRequests = config.maxConcurrentRequests;
  }

  checkRateLimit(ip: string): boolean {
    const now = Date.now();
    
    // Periodic cleanup to prevent memory leaks
    if (now - this.lastCleanup > this.cleanupInterval) {
      this.cleanup(now);
      this.lastCleanup = now;
    }

    let window = this.windows.get(ip);
    if (!window) {
      window = new SlidingWindow(this.windowSize);
      this.windows.set(ip, window);
    }

    const requestCount = window.addRequest(now);
    return requestCount <= this.maxRequests;
  }

  private cleanup(now: number): void {
    for (const [ip, window] of this.windows.entries()) {
      if (window.isExpired(now)) {
        this.windows.delete(ip);
      }
    }
  }
}

// Lightweight request filter
class LightweightRequestFilter {
  private config: RegionalSecurityConfig;
  private ipCache = new Map<string, IPSecurityState>();
  private readonly maxCacheSize: number;

  constructor(region: string) {
    this.config = REGIONAL_SECURITY_CONFIG[region] || REGIONAL_SECURITY_CONFIG.US;
    this.maxCacheSize = this.config.ipCacheSize;
  }

  validateRequest(req: Request): SecurityResult {
    const startTime = Date.now();

    try {
      // Get client IP with fallback
      const clientIP = req.ip || req.socket?.remoteAddress || 'unknown';

      // 1. Quick size check (no parsing required)
      const contentLength = parseInt(req.headers['content-length'] || '0');
      if (contentLength > this.config.maxRequestSize) {
        return { blocked: true, reason: 'oversized', severity: 3 };
      }

      // 2. Method validation (string comparison only)
      const allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'];
      if (!allowedMethods.includes(req.method)) {
        return { blocked: true, reason: 'method', severity: 2 };
      }

      // 3. Cached IP check (fastest path)
      const ipState = this.ipCache.get(clientIP);
      if (ipState?.blocked && Date.now() < ipState.blockedUntil) {
        return { blocked: true, reason: 'ip_blocked', severity: 5 };
      }

      // 4. Basic attack pattern detection (only if enabled for region)
      if (this.config.enableThreatIntel) {
        const attackResult = this.detectAttackPatterns(req);
        if (attackResult) {
          this.updateIPState(clientIP, true, 300000); // Block for 5 minutes
          return {
            blocked: true,
            reason: `${attackResult.type}: ${attackResult.description}`,
            severity: attackResult.score
          };
        }
      }

      // 5. Update IP state for successful validation
      this.updateIPState(clientIP, false, 0);

      // Check if validation took too long
      const duration = Date.now() - startTime;
      if (duration > this.config.validationTimeout) {
        logger.warn(`Security validation slow: ${duration}ms for ${clientIP} (limit: ${this.config.validationTimeout}ms)`);
      }

      return { blocked: false };

    } catch (error) {
      logger.error('Security validation error:', error as Error);
      // Fail open for performance - don't block on errors
      return { blocked: false };
    }
  }

  private detectAttackPatterns(req: Request): { type: string; score: number; description: string } | null {
    try {
      // Normalize and combine URL, headers, and body for analysis
      const url = normalizeInput(req.url || '');
      const body = req.body ? normalizeInput(JSON.stringify(req.body)) : '';
      const userAgent = normalizeInput(req.headers['user-agent'] || '');
      const referer = normalizeInput(req.headers['referer'] || '');

      const payload = `${url} ${body} ${userAgent} ${referer}`;

      let totalScore = 0;
      let detectedPatterns: Array<{ type: string; score: number; description: string }> = [];

      // Check each pattern and accumulate scores
      for (const [type, config] of Object.entries(ATTACK_PATTERNS)) {
        if (config.pattern.test(payload)) {
          totalScore += config.score;
          detectedPatterns.push({
            type,
            score: config.score,
            description: config.description
          });
        }
      }

      // Return highest scoring pattern if total score exceeds threshold
      if (totalScore >= 5 && detectedPatterns.length > 0) {
        // Return the highest scoring pattern
        return detectedPatterns.reduce((highest, current) =>
          current.score > highest.score ? current : highest
        );
      }

      return null;
    } catch (error) {
      logger.error('Attack pattern detection error:', error as Error);
      return null;
    }
  }

  private updateIPState(ip: string, blocked: boolean, blockDuration: number): void {
    // Prevent cache from growing too large
    if (this.ipCache.size >= this.maxCacheSize) {
      // Remove oldest entries (simple FIFO)
      const firstKey = this.ipCache.keys().next().value;
      if (firstKey) {
        this.ipCache.delete(firstKey);
      }
    }

    const now = Date.now();
    const existing = this.ipCache.get(ip);
    
    this.ipCache.set(ip, {
      blocked,
      blockedUntil: blocked ? now + blockDuration : 0,
      requestCount: (existing?.requestCount || 0) + 1,
      lastRequest: now,
      threatLevel: blocked ? Math.min((existing?.threatLevel || 0) + 1, 10) : Math.max((existing?.threatLevel || 0) - 0.1, 0)
    });
  }
}

// Initialize components based on region
const region = process.env.REGION || 'US';
const requestFilter = new LightweightRequestFilter(region);
const rateLimiter = new RegionalRateLimiter(region);

// Main security middleware
export const lightweightSecurityMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  const startTime = Date.now();

  try {
    // Get client IP with fallback
    const clientIP = req.ip || req.socket.remoteAddress || 'unknown';

    // 1. Ultra-fast request validation
    const validationResult = requestFilter.validateRequest(req);
    if (validationResult.blocked) {
      logger.warn(`Request blocked: ${validationResult.reason} from ${clientIP}`);
      res.status(400).json({
        error: 'Request blocked',
        reason: validationResult.reason,
        'request-id': req.headers['x-request-id'] || 'unknown'
      });
      return;
    }

    // 2. Rate limiting check
    if (!rateLimiter.checkRateLimit(clientIP)) {
      logger.warn(`Rate limit exceeded for ${clientIP}`);
      res.status(429).json({
        error: 'Rate limit exceeded',
        'request-id': req.headers['x-request-id'] || 'unknown'
      });
      return;
    }

    // 3. Performance monitoring
    const duration = Date.now() - startTime;
    if (duration > 5) { // Log if over 5ms
      logger.warn(`Security middleware slow: ${duration}ms for ${clientIP} (region: ${region})`);
    }

    next();

  } catch (error) {
    logger.error('Security middleware error:', error as Error);
    // Fail open - don't block requests on middleware errors
    next();
  }
};

// Export for testing and monitoring
export { requestFilter, rateLimiter, REGIONAL_SECURITY_CONFIG };
